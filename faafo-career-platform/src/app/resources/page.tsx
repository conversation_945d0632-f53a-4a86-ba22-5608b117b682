'use client';

import React from 'react';
import Link from 'next/link';
import { ExternalLink, Heart, Brain, DollarSign, Users, BookOpen, Video, Headphones } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface Resource {
  id: string;
  title: string;
  description: string;
  url: string;
  type: 'article' | 'video' | 'podcast' | 'book';
  category: 'fear' | 'financial' | 'imposter' | 'motivation' | 'planning';
  author?: string;
  duration?: string;
}

const mindsetResources: Resource[] = [
  {
    id: '1',
    title: 'Overcoming Fear of Career Change',
    description: 'A comprehensive guide to understanding and conquering the fears that hold you back from making a career transition.',
    url: 'https://www.themuse.com/advice/how-to-overcome-fear-of-career-change',
    type: 'article',
    category: 'fear',
    author: 'The Muse',
    duration: '8 min read'
  },
  {
    id: '2',
    title: 'Financial Planning for Career Transitions',
    description: 'Learn how to build a financial safety net and plan your finances before making a major career change.',
    url: 'https://www.nerdwallet.com/article/finance/financial-planning-career-change',
    type: 'article',
    category: 'financial',
    author: 'NerdWallet',
    duration: '10 min read'
  },
  {
    id: '3',
    title: 'Dealing with Imposter Syndrome',
    description: 'Strategies to overcome self-doubt and imposter syndrome when pursuing new career opportunities.',
    url: 'https://hbr.org/2021/02/stop-telling-women-they-have-imposter-syndrome',
    type: 'article',
    category: 'imposter',
    author: 'Harvard Business Review',
    duration: '12 min read'
  },
  {
    id: '4',
    title: 'The Psychology of Career Change',
    description: 'Understanding the emotional journey of career transitions and how to navigate the psychological challenges.',
    url: 'https://www.youtube.com/watch?v=example1',
    type: 'video',
    category: 'motivation',
    author: 'TED Talks',
    duration: '18 min'
  },
  {
    id: '5',
    title: 'Building Confidence During Career Transitions',
    description: 'Practical techniques to build and maintain confidence while exploring new career paths.',
    url: 'https://www.psychologytoday.com/us/blog/career-transitions',
    type: 'article',
    category: 'motivation',
    author: 'Psychology Today',
    duration: '7 min read'
  },
  {
    id: '6',
    title: 'The Career Change Podcast',
    description: 'Weekly episodes featuring real stories of successful career changers and expert advice.',
    url: 'https://www.example-podcast.com/career-change',
    type: 'podcast',
    category: 'motivation',
    author: 'Career Change Network',
    duration: 'Weekly episodes'
  },
  {
    id: '7',
    title: 'Emergency Fund Calculator and Guide',
    description: 'Tools and strategies for building an emergency fund to support your career transition.',
    url: 'https://www.bankrate.com/calculators/savings/emergency-fund-calculator/',
    type: 'article',
    category: 'financial',
    author: 'Bankrate',
    duration: '5 min read'
  },
  {
    id: '8',
    title: 'Mindfulness for Career Anxiety',
    description: 'Meditation and mindfulness techniques to manage anxiety and stress during career changes.',
    url: 'https://www.headspace.com/work-life-balance/career-change',
    type: 'article',
    category: 'fear',
    author: 'Headspace',
    duration: '6 min read'
  }
];

const categoryInfo = {
  fear: {
    title: 'Overcoming Fear & Anxiety',
    icon: Heart,
    description: 'Resources to help you manage fear, anxiety, and uncertainty during career transitions.',
    color: 'text-red-600 dark:text-red-400'
  },
  financial: {
    title: 'Financial Planning',
    icon: DollarSign,
    description: 'Practical advice for managing finances and building security during career changes.',
    color: 'text-green-600 dark:text-green-400'
  },
  imposter: {
    title: 'Imposter Syndrome',
    icon: Brain,
    description: 'Strategies to overcome self-doubt and build confidence in your abilities.',
    color: 'text-purple-600 dark:text-purple-400'
  },
  motivation: {
    title: 'Motivation & Mindset',
    icon: Users,
    description: 'Inspiration and practical advice to maintain motivation throughout your journey.',
    color: 'text-blue-600 dark:text-blue-400'
  },
  planning: {
    title: 'Strategic Planning',
    icon: BookOpen,
    description: 'Frameworks and tools for planning and executing your career transition.',
    color: 'text-orange-600 dark:text-orange-400'
  }
};

const getResourceIcon = (type: Resource['type']) => {
  switch (type) {
    case 'article':
      return BookOpen;
    case 'video':
      return Video;
    case 'podcast':
      return Headphones;
    case 'book':
      return BookOpen;
    default:
      return BookOpen;
  }
};

export default function ResourcesPage() {
  const categories = Object.keys(categoryInfo) as Array<keyof typeof categoryInfo>;

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Mindset & Support Resources</h1>
        <p className="text-lg text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
          Curated resources to help you overcome common emotional and psychological challenges 
          during your career transition journey. Build confidence, manage anxiety, and develop 
          the mindset needed for successful change.
        </p>
      </div>

      <div className="space-y-12">
        {categories.map((categoryKey) => {
          const category = categoryInfo[categoryKey];
          const categoryResources = mindsetResources.filter(resource => resource.category === categoryKey);
          
          if (categoryResources.length === 0) return null;

          const IconComponent = category.icon;

          return (
            <div key={categoryKey} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
              <div className="flex items-center gap-3 mb-6">
                <IconComponent className={`h-8 w-8 ${category.color}`} />
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {category.title}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400">
                    {category.description}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {categoryResources.map((resource) => {
                  const ResourceIcon = getResourceIcon(resource.type);
                  
                  return (
                    <div
                      key={resource.id}
                      className="border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start gap-3 mb-3">
                        <ResourceIcon className="h-5 w-5 text-gray-500 dark:text-gray-400 mt-1 flex-shrink-0" />
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">
                            {resource.title}
                          </h3>
                          {resource.author && (
                            <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                              by {resource.author} • {resource.duration}
                            </p>
                          )}
                        </div>
                      </div>
                      
                      <p className="text-gray-700 dark:text-gray-300 mb-4 text-sm leading-relaxed">
                        {resource.description}
                      </p>
                      
                      <Button
                        asChild
                        variant="outline"
                        size="sm"
                        className="w-full"
                      >
                        <a
                          href={resource.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center gap-2"
                        >
                          <span className="capitalize">{resource.type === 'article' ? 'Read' : resource.type === 'video' ? 'Watch' : 'Listen'}</span>
                          <ExternalLink className="h-4 w-4" />
                        </a>
                      </Button>
                    </div>
                  );
                })}
              </div>
            </div>
          );
        })}
      </div>

      <div className="mt-12 text-center bg-blue-50 dark:bg-blue-900/20 p-8 rounded-lg">
        <h3 className="text-xl font-semibold text-blue-900 dark:text-blue-100 mb-4">
          Need More Personalized Support?
        </h3>
        <p className="text-blue-700 dark:text-blue-300 mb-6 max-w-2xl mx-auto">
          Join our community forum to connect with others on similar journeys, share experiences, 
          and get support from people who understand what you're going through.
        </p>
        <div className="flex gap-4 justify-center">
          <Button asChild>
            <Link href="/forum">Join Community</Link>
          </Button>
          <Button asChild variant="outline">
            <Link href="/assessment">Take Assessment</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
